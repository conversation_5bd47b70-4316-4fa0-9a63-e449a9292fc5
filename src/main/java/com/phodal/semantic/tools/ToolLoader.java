package com.phodal.semantic.tools;

import com.phodal.semantic.plugins.general.ComplianceTools;
import com.phodal.semantic.plugins.general.GeneralTools;
import com.phodal.semantic.plugins.general.PlanningTools;
import com.phodal.semantic.plugins.general.QATools;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.ai.tool.ToolCallbackProvider;
import org.springframework.ai.tool.method.MethodToolCallbackProvider;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 工具加载器，负责动态加载和管理工具函数
 * 使用 Spring AI 的 MethodToolCallbackProvider 来处理工具注册
 */
@Component
public class ToolLoader {

    private static final Logger logger = LoggerFactory.getLogger(ToolLoader.class);
    private final Map<String, ToolCallbackProvider> businessToolsCache = new ConcurrentHashMap<>();
    private final List<ToolCallbackProvider> generalToolProviders = new ArrayList<>();

    public ToolLoader() {
        initializeGeneralTools();
    }
    
    /**
     * 初始化通用工具
     */
    private void initializeGeneralTools() {
        try {
            // 创建通用工具的 ToolCallbackProvider
            ToolCallbackProvider generalToolProvider = MethodToolCallbackProvider.builder()
                    .toolObjects(new GeneralTools())
                    .build();
            generalToolProviders.add(generalToolProvider);

            logger.info("Initialized {} general tool providers", generalToolProviders.size());
        } catch (Exception e) {
            logger.error("Failed to initialize general tools", e);
        }
    }

    /**
     * 获取指定业务的工具回调列表
     * @param businessName 业务名称
     * @return 工具回调列表
     */
    public List<ToolCallback> getToolsForBusiness(String businessName) {
        ToolCallbackProvider provider = businessToolsCache.computeIfAbsent(businessName, this::loadBusinessToolProvider);
        return provider != null ? Arrays.asList(provider.getToolCallbacks()) : new ArrayList<>();
    }

    /**
     * 获取所有通用工具回调
     * @return 通用工具回调列表
     */
    public List<ToolCallback> getGeneralTools() {
        List<ToolCallback> allGeneralTools = new ArrayList<>();
        for (ToolCallbackProvider provider : generalToolProviders) {
            allGeneralTools.addAll(Arrays.asList(provider.getToolCallbacks()));
        }
        return allGeneralTools;
    }

    /**
     * 获取所有可用工具（通用工具 + 业务特定工具）
     * @param businessName 业务名称
     * @return 所有可用工具回调列表
     */
    public List<ToolCallback> getAllAvailableTools(String businessName) {
        List<ToolCallback> allTools = new ArrayList<>(getGeneralTools());
        allTools.addAll(getToolsForBusiness(businessName));
        return allTools;
    }

    /**
     * 获取所有可用工具对象（通用工具 + 业务特定工具）
     * @param businessName 业务名称
     * @return 所有可用工具对象列表
     */
    public List<Object> getAllAvailableToolObjects(String businessName) {
        List<Object> allToolObjects = new ArrayList<>();

        // 添加通用工具对象
        allToolObjects.add(new GeneralTools());

        // 添加业务特定工具对象
        Object businessToolObject = getBusinessToolObject(businessName);
        if (businessToolObject != null) {
            allToolObjects.add(businessToolObject);
        }

        return allToolObjects;
    }

    /**
     * 获取业务特定的工具对象
     */
    private Object getBusinessToolObject(String businessName) {
        switch (businessName.toLowerCase()) {
            case "qa":
            case "tuoguan":
                return new QATools();
            case "compliance_review":
                return new ComplianceTools();
            case "plan":
                return new PlanningTools();
            default:
                return null;
        }
    }
    
    /**
     * 为特定业务加载工具提供者
     */
    private ToolCallbackProvider loadBusinessToolProvider(String businessName) {
        try {
            Object toolObject = null;

            switch (businessName.toLowerCase()) {
                case "qa":
                case "tuoguan":
                    // 加载问答相关工具
                    toolObject = new QATools();
                    break;

                case "compliance_review":
                    // 加载合规审核工具
                    toolObject = new ComplianceTools();
                    break;

                case "plan":
                    // 加载规划工具
                    toolObject = new PlanningTools();
                    break;

                default:
                    logger.info("No specific tools found for business: {}", businessName);
                    return null;
            }

            if (toolObject != null) {
                ToolCallbackProvider provider = MethodToolCallbackProvider.builder()
                        .toolObjects(toolObject)
                        .build();

                logger.info("Loaded tool provider for business: {} with {} tools",
                    businessName, provider.getToolCallbacks().length);
                return provider;
            }

        } catch (Exception e) {
            logger.error("Failed to load tools for business: {}", businessName, e);
        }

        return null;
    }

    /**
     * 清除工具缓存
     */
    public void clearCache() {
        businessToolsCache.clear();
        logger.info("Tool cache cleared");
    }

    /**
     * 获取缓存的业务工具数量
     */
    public int getCacheSize() {
        return businessToolsCache.size();
    }

    /**
     * 注册自定义工具
     * @param businessName 业务名称
     * @param toolInstance 工具实例
     */
    public void registerCustomTool(String businessName, Object toolInstance) {
        try {
            ToolCallbackProvider customProvider = MethodToolCallbackProvider.builder()
                    .toolObjects(toolInstance)
                    .build();

            businessToolsCache.put(businessName, customProvider);

            logger.info("Registered custom tool provider for business: {} with {} tools",
                businessName, customProvider.getToolCallbacks().length);
        } catch (Exception e) {
            logger.error("Failed to register custom tool for business: {}", businessName, e);
        }
    }

    /**
     * 获取工具统计信息
     */
    public Map<String, Object> getToolStats() {
        Map<String, Object> stats = new java.util.HashMap<>();
        stats.put("generalToolProviders", generalToolProviders.size());
        stats.put("businessToolProviders", businessToolsCache.size());

        int totalGeneralTools = 0;
        for (ToolCallbackProvider provider : generalToolProviders) {
            totalGeneralTools += provider.getToolCallbacks().length;
        }
        stats.put("totalGeneralTools", totalGeneralTools);

        int totalBusinessTools = 0;
        for (ToolCallbackProvider provider : businessToolsCache.values()) {
            totalBusinessTools += provider.getToolCallbacks().length;
        }
        stats.put("totalBusinessTools", totalBusinessTools);
        stats.put("totalTools", totalGeneralTools + totalBusinessTools);

        return stats;
    }
}
