{"schema": 1, "type": "completion", "description": "交互内容中存在和问题相关的信息时，请调用这个函数，根据交互内容或给出的文本，回答用户的问题", "execution_settings": {"default": {"ai_model_id": "deepseek-chat", "max_tokens": 8192, "temperature": 0, "presence_penalty": 0.6, "frequency_penalty": 0.0}}, "input_variables": [{"name": "input", "description": "交互内容中的历史对话或需要问答的文本", "default": "", "is_required": true}, {"name": "query", "description": "用户的问题或需求", "default": "", "is_required": true}]}